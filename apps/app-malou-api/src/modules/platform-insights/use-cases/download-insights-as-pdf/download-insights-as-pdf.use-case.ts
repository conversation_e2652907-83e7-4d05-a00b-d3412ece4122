import { singleton } from 'tsyringe';
import { v4 } from 'uuid';

import { MalouErrorCode } from '@malou-io/package-utils';

import { Config } from ':config';
import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import PuppeteerService from ':microservices/puppeteer-service';
import { DownloadInsightsAsPdfBody } from ':modules/platform-insights/platform-insights.types';
import { RestartLambdaService } from ':services/aws/lambda/restart-lambda.service';

@singleton()
export class DownloadInsightsAsPdfUseCase {
    constructor(
        private readonly _puppeteerService: PuppeteerService,
        private readonly _restartLambdaService: RestartLambdaService
    ) {}

    async execute(body: DownloadInsightsAsPdfBody): Promise<string> {
        const baseUrl = process.env.BASE_URL;
        const fullCallBackUrl = `${baseUrl}/${body.callbackUrl}`;
        const bucketPath = this._computeBucketPath(body);
        const lambdaCode = this._getLambdaCode({
            baseUrl,
            jwtToken: body.jwtToken,
            fullCallBackUrl,
            bucketUrl: process.env.AWS_BUCKET,
            bucketPath: bucketPath,
            pdfParams: body.params,
        });
        let sandboxResponse = await this._puppeteerService.usePuppeteerSandbox(lambdaCode);
        if (sandboxResponse.log?.includes('ERROR')) {
            logger.warn('[DownloadInsightsAsPdfUseCase] Error in puppeteer sandbox, restarting lambda and retrying', { sandboxResponse });
            await this._restartLambdaService.restart(Config.services.puppeteer.arn);
            sandboxResponse = await this._puppeteerService.usePuppeteerSandbox(lambdaCode);
        }
        if (sandboxResponse.log?.includes('ERROR')) {
            logger.warn('[DownloadInsightsAsPdfUseCase] Error even after restarting lambda, throwing error', { sandboxResponse });
            throw new MalouError(MalouErrorCode.DOWNLOAD_INSIGHTS_AS_PDF_FAILED, { metadata: { logs: sandboxResponse.log } });
        }

        const pdfUrl = sandboxResponse.res;
        return pdfUrl;
    }

    private _computeBucketPath(body: DownloadInsightsAsPdfBody): string {
        const dateISOString = new Date().toISOString();
        let bucketPath = 'insights-pdf';
        if (body.restaurantId) {
            bucketPath += `/${body.restaurantId}`;
        } else {
            bucketPath += `/aggregated/${body.userId}`;
        }
        bucketPath += `/${body.insightTab}-${dateISOString}-${v4()}.pdf`;
        return bucketPath;
    }

    private _getLambdaCode(params: {
        baseUrl: string;
        jwtToken: string;
        fullCallBackUrl: string;
        bucketUrl: string;
        bucketPath: string;
        pdfParams: string;
    }): string {
        return `try {
            const browser = await puppeteer.launch();
            const page = await browser.newPage();
            page.setDefaultNavigationTimeout(45_000)

            // Store all console logs
            const consoleLogs = [];
            page.on('console', msg => {
                consoleLogs.push({
                    type: msg.type(),
                    text: msg.text(),
                    args: msg.args()
                });
            });

            // Store all requests
            const requests = [];
            const responses = [];

            // Intercept non useful scripts
            ${this._puppeteerService.blockRequests({})}

            page.on("response", res => {
                responses.push({
                    url: res.url(),
                    status: res.status(),
                    contentType: res.headers()['content-type']
                });
            });

            const a4Ratio = 297 / 210 ;
            const width = 1025;
            const height = Math.round(width * a4Ratio);
            await page.setViewport({ width, height, deviceScaleFactor: 3 });
            await page.emulateMediaType('screen');

            // We fetch a fake page because it loads faster
            // const notFoundUrl = '${params.baseUrl}/404';
            const notFoundUrl = 'https://development.omni.malou.io/404';
            await page.goto(notFoundUrl);
            const jwtToken = '${params.jwtToken}';
            const params = '${params.pdfParams}';
            await page.evaluate(([jwtTokenParam, queryParams]) => {
                localStorage.setItem('jwtToken', jwtTokenParam);
                localStorage.setItem('downloadInsightsParams', queryParams);
            }, [jwtToken, params]);

            // const insightsUrl = '${params.fullCallBackUrl}';
            const insightsUrl = 'https://development.omni.malou.io/statistics-pdf/6268039d8de2974d89ebd445/e-reputation';
            // networkidle2 because we have a growthbook request that never ends
            await page.goto(insightsUrl, { waitUntil: 'networkidle2' });
            // using page.waitForNetworkIdle after page.goto seems to not work

            // Extra waiting for front to finish loading
            await new Promise((resolve) => setTimeout(resolve, 3500));

            const buffer = await page.pdf({
                width,
                height,
                printBackground: true,
                margin: {
                    top: '1cm',
                },
                timeout: 0,
            });

            const pdfSizeInKB = buffer.byteLength / 1024;
            console.log("pdfSizeInKB : ", pdfSizeInKB);
            const MIN_SIZE_IN_KB = 10;
            if (pdfSizeInKB < MIN_SIZE_IN_KB) {
                console.log("pdfSizeInKB in not enough : ", pdfSizeInKB);
                throw new Error("pdfSizeInKB in not enough");
            }

            const s3Result = await s3
                .upload({
                    Bucket: '${params.bucketUrl}',
                    Key: '${params.bucketPath}',
                    Body: buffer,
                    ContentType: 'application/pdf',
                    ACL: 'public-read',
                })
                .promise();

            // Log everything before returning
            console.log('=== CONSOLE LOGS ===');
            consoleLogs.forEach(log => console.log(JSON.stringify(log)));
            
            console.log('=== REQUESTS ===');
            requests.forEach(req => console.log(JSON.stringify(req)));
            
            console.log('=== RESPONSES ===');
            responses.forEach(res => console.log(JSON.stringify(res)));

            // return {logs: JSON.stringify(consoleLogs), requests: JSON.stringify(requests), responses: JSON.stringify(responses)};

            return s3Result?.Location
        } catch (e) {
            console.info('ERROR');
            console.info(JSON.stringify(e, Object.getOwnPropertyNames(e)))
        }`;
    }
}
